<?php
/**
 * <AUTHOR>
 * @student_id  000886844
 * @version     1.0
 * @date        2025-08-08
 * @purpose     Server-side script for Assignment 4: Infinite Scroll Quotes
 *              Fetches paginated quotes from the database using PDO and returns them as JSON.
 * @package     Assignment4
 */

// ----- CONFIG -----
$limit = 20; // Number of quotes per request

// ----- DATABASE CONNECTION -----
$host = 'localhost';
$db   = '10260_assignment_4'; // Your actual DB name
$user = 'root';               // XAMPP default user
$pass = '';                   // XAMPP default password is empty
$charset = 'utf8mb4';

$dsn = "mysql:host=$host;dbname=$db;charset=$charset";

$options = [
    PDO::ATTR_ERRMODE            => PDO::ERRMODE_EXCEPTION,
    PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
    PDO::ATTR_EMULATE_PREPARES   => false,
];

try {
    $pdo = new PDO($dsn, $user, $pass, $options);
} catch (PDOException $e) {
    echo json_encode(['error' => 'Database connection failed']);
    exit;
}

// ----- INPUT VALIDATION -----
$page = filter_input(INPUT_GET, 'page', FILTER_VALIDATE_INT, [
    'options' => ['default' => 1, 'min_range' => 1]
]);
$offset = ($page - 1) * $limit;

// ----- FETCH QUOTES -----
try {
    $stmt = $pdo->prepare("
        SELECT q.quote_id, q.quote_text, a.author_name
        FROM quotes q
        JOIN authors a ON q.author_id = a.author_id
        ORDER BY q.quote_id ASC
        LIMIT :limit OFFSET :offset
    ");

    $stmt->bindValue(':limit', $limit, PDO::PARAM_INT);
    $stmt->bindValue(':offset', $offset, PDO::PARAM_INT);
    $stmt->execute();

    $quotes = $stmt->fetchAll();

    header('Content-Type: application/json; charset=utf-8');
    echo json_encode($quotes);

} catch (PDOException $e) {
    http_response_code(500);
    echo json_encode(['error' => 'Query failed']);
    exit;
}