<?php
/**
 * <AUTHOR>
 * @student_id  000886844
 * @version     1.0
 * @date        2025-08-08
 * @purpose     Server-side script for Assignment 4: Infinite Scroll Quotes
 *              Fetches paginated quotes from the database using PDO and returns them as JSON array of HTML cards.
 * @package     Assignment4
 */

// ----- CONFIG -----
$limit = 20; // Number of quotes per request

// ----- DATABASE CONNECTION -----
$host = 'localhost';
$db   = '10260_assignment_4'; // Your actual DB name
$user = 'root';               // XAMPP default user
$pass = '';                   // XAMPP default password is empty
$charset = 'utf8mb4';

$options = [
    PDO::ATTR_ERRMODE            => PDO::ERRMODE_EXCEPTION,
    PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
    PDO::ATTR_EMULATE_PREPARES   => false,
];

try {
    // First try to connect without specifying database to create it if needed
    $dsn_no_db = "mysql:host=$host;charset=$charset";
    $pdo_temp = new PDO($dsn_no_db, $user, $pass, $options);

    // Create database if it doesn't exist
    $pdo_temp->exec("CREATE DATABASE IF NOT EXISTS `$db`");

    // Now connect to the specific database
    $dsn = "mysql:host=$host;dbname=$db;charset=$charset";
    $pdo = new PDO($dsn, $user, $pass, $options);

} catch (PDOException $e) {
    http_response_code(500);
    echo json_encode(['error' => 'Database connection failed: ' . $e->getMessage()]);
    exit;
}

// ----- INPUT VALIDATION -----
$page = filter_input(INPUT_GET, 'page', FILTER_VALIDATE_INT, [
    'options' => ['default' => 1, 'min_range' => 1]
]);

if ($page === false || $page < 1) {
    http_response_code(400);
    echo json_encode(['error' => 'Invalid page parameter']);
    exit;
}

$offset = ($page - 1) * $limit;

/**
 * Generates a Bootstrap 5 card HTML string for a quote
 * @param string $quote_text The quote text
 * @param string $author_name The author's name
 * @return string The HTML card string
 */
function generateQuoteCard($quote_text, $author_name) {
    // Sanitize the data for HTML output
    $safe_quote = htmlspecialchars($quote_text, ENT_QUOTES, 'UTF-8');
    $safe_author = htmlspecialchars($author_name, ENT_QUOTES, 'UTF-8');
    
    return '<div class="card mb-3 a4card w-100">' .
           '<div class="card-header">' . $safe_author . '</div>' .
           '<div class="card-body d-flex align-items-center">' .
           '<p class="card-text w-100">' . $safe_quote . '</p>' .
           '</div>' .
           '</div>';
}

// ----- FETCH QUOTES -----
try {
    $stmt = $pdo->prepare("
        SELECT q.quote_text, a.author_name
        FROM quotes q
        JOIN authors a ON q.author_id = a.author_id
        ORDER BY q.quote_id ASC
        LIMIT :limit OFFSET :offset
    ");

    $stmt->bindValue(':limit', $limit, PDO::PARAM_INT);
    $stmt->bindValue(':offset', $offset, PDO::PARAM_INT);
    $stmt->execute();

    $quotes = $stmt->fetchAll();

    // Generate HTML cards array
    $cards = [];
    foreach ($quotes as $quote) {
        $cards[] = generateQuoteCard($quote['quote_text'], $quote['author_name']);
    }

    // Return JSON encoded array of HTML cards
    header('Content-Type: application/json; charset=utf-8');
    echo json_encode($cards);

} catch (PDOException $e) {
    http_response_code(500);
    echo json_encode(['error' => 'Query failed']);
    exit;
}
